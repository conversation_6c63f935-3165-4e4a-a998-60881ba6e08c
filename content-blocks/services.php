<?php
// Services section for flexible content block
$anchor    = get_sub_field('anchor');
$heading   = get_sub_field('heading');
$text      = get_sub_field('text');
$services  = get_sub_field('services');
$button_1  = get_sub_field('button_1');
$button_2  = get_sub_field('button_2');
?>
<section class="services" <?php if ($anchor) : ?>id="<?php echo esc_attr($anchor); ?>"<?php endif; ?>>
    <div class="container">
        <div class="row services__text">
            <?php if ($heading) : ?>
                <h2><?php echo esc_html($heading); ?></h2>
            <?php endif; ?>
            <?php if ($text) : ?>
                <?php echo wp_kses_post($text); ?>
            <?php endif; ?>
        </div>
        <?php if ($services) : ?>
            <div class="row flex flex-wrap services__items">
                <?php 
                $video_preview = false;
                foreach ($services as $service) :
                ?>
                    <div class="col">
                        <?php if ($service['icon']) : ?>
                            <div class="service__icon">
                                <?php echo $service['icon']; ?>
                            </div>
                        <?php endif; ?>
                        <?php if ($service['heading']) : ?>
                            <h3><?php echo wp_kses_post($service['heading']); ?></h3>
                        <?php endif; ?>
                        <?php if ($service['text']) : ?>
                            <?php echo wp_kses_post($service['text']); ?>
                        <?php endif; ?>
                        <?php 
                        if ($service['video_preview']) : 
                        $video_preview = true;
                        ?>
                            <a class="link" href="#video_popup" data-video-url="<?php echo esc_url($service['video_preview']); ?>">
                                <?php echo __('Video preview', 'survilla'); ?>
                            </a>
                        <?php endif; ?>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
        <?php if ($button_1 || $button_2) : ?>
            <div class="row flex justify-center flex-wrap row--buttons">
                <?php if ($button_1) : ?>
                    <a class="button button--cta" href="<?php echo esc_url($button_1['url']); ?>" target="<?php echo esc_attr($button_1['target']); ?>">
                        <?php echo esc_html($button_1['title']); ?>
                    </a>
                <?php endif; ?>
                <?php if ($button_2) : ?>
                    <a class="button button--link" href="<?php echo esc_url($button_2['url']); ?>" target="<?php echo esc_attr($button_2['target']); ?>">
                        <?php echo esc_html($button_2['title']); ?>
                    </a>
                <?php endif; ?>
            </div>
        <?php endif; ?>
    </div>
</section>

<?php if ($video_preview) : ?>
    <div class="popup" id="video_popup">
        <!-- Video content will be populated dynamically by JavaScript -->
        <div class="video-element"></div>
        <div class="popup__close"></div>
    </div>
<?php endif; ?>