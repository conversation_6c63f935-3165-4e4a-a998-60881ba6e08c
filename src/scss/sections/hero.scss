@use '../abstracts/variables' as variables;

.hero {
    // Full viewport height minus header and admin bar
    min-height: 100vh;
    min-height: calc(100vh - 54px); // Account for fixed header
    background: linear-gradient(180deg, variables.$dark-blue 23.5%, variables.$darker-blue 44.5%, variables.$mid-blue 55%, variables.$light-blue 72%, #213685 86%, variables.$dark-blue 100%);
    color: variables.$inverted-text-color;
    display: flex;
    flex-direction: column;
    padding: 67px 0 40px;

    // Account for WordPress admin bar when logged in
    .admin-bar & {
        min-height: calc(100vh - 54px - 32px); // Header + admin bar on desktop

        @media screen and (max-width: 782px) {
            min-height: calc(100vh - 54px - 46px); // Header + admin bar on mobile
        }

        @media screen and (max-width: 600px) {
            min-height: calc(100vh - 54px); // Admin bar becomes sticky on small screens
        }
    }

    // Ensure content is centered vertically
    .container {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;
    }

    h1 {
        margin-bottom: 43px;
    }
}

.hero__content {
    @media screen and (max-width: variables.$breakpoint-md-max) {
        br {
            display: none;
        }
    }
}

.hero__items {
    margin-top: 30px;
    margin-bottom: 15px;
    gap: 20px;
    display: flex;
    flex-wrap: wrap;

    // Default layout: horizontal (image beside icons)
    @media screen and (min-width: variables.$breakpoint-md-min) {
        flex-direction: row;
        align-items: center;
    }

    // Tall screens: vertical layout (image above icons)
    @media screen and (min-width: variables.$breakpoint-md-min) and (min-height: 800px) {
        flex-direction: column;
        align-items: center;
        gap: 40px;
    }

    // Mobile and tablet: always center and stack appropriately
    @media screen and (max-width: variables.$breakpoint-md-max) {
        justify-content: center;
        flex-direction: column;
        align-items: center;
        gap: 30px;
    }

    .col {
        display: flex;
        flex-direction: column;
        align-items: center;
    }
}

.hero__items__img {
    align-self: center;

    // Default horizontal layout (image beside icons)
    @media screen and (min-width: variables.$breakpoint-md-min) {
        flex: 0 0 calc(440 / 1400 * 100%);
        max-width: calc(440 / 1400 * 100% + 20px);
        margin-right: -20px;
    }

    // Tall screens: vertical layout (image above icons)
    @media screen and (min-width: variables.$breakpoint-md-min) and (min-height: 800px) {
        flex: 0 0 auto;
        max-width: 100%;
        margin-right: 0;
        margin-bottom: 20px;
    }

    // Mobile and tablet
    @media screen and (max-width: variables.$breakpoint-md-max) {
        flex: 0 0 100%;
        max-width: 100%;
        margin-right: 0;
    }

    img {
        width: 580px;
        max-width: 100%;
        height: auto;
    }
}

.hero__items__icon {
    // Default horizontal layout (beside image)
    @media screen and (min-width: variables.$breakpoint-md-min) {
        flex: 0 0 calc(980 / 1400 * 100% / 3 - 20px);
        max-width: calc(980 / 1400 * 100% / 3 - 20px);
        padding-top: 14.5px;
    }

    // Tall screens: vertical layout (below image)
    @media screen and (min-width: variables.$breakpoint-md-min) and (min-height: 800px) {
        flex: 0 0 calc(100% / 3 - 40px / 3);
        max-width: calc(100% / 3 - 40px / 3);
        padding-top: 0;
    }

    // Tablet
    @media screen and (max-width: variables.$breakpoint-md-max) and (min-width: variables.$breakpoint-sm-min) {
        flex: 0 0 320px;
        max-width: calc(100% / 3 - 40px / 3);
        padding-top: 0;
    }

    // Mobile
    @media screen and (max-width: variables.$breakpoint-sm-max) {
        flex: 0 0 100%;
        max-width: 100%;
        padding-top: 0;
    }

    img, svg {
        width: auto;
        height: 117px;
        display: block;
        margin: 0 auto;
    }

    &:nth-child(3) {
        img, svg {
            margin-top: 6.5px;
            margin-bottom: 6.5px;
            height: 104px;
        }
    }

    h2 {
        margin-top: 13px;
        margin-bottom: 13px;
    }

    p {
        width: 250px;
        max-width: 100%;
    }
}

// Icons wrapper for tall screens layout
.hero__items__icons {
    // Only apply on tall screens where icons are grouped together
    @media screen and (min-width: variables.$breakpoint-md-min) and (min-height: 800px) {
        display: flex;
        flex-direction: row;
        justify-content: center;
        gap: 20px;
        width: 100%;
        max-width: 1200px;
    }
}
