@use '../abstracts/variables' as variables;

.features {
    padding-top: 80px;
    padding-bottom: 100px;
}

.features__items {
    margin-top: 50px;
    gap: 20px;

    .col {
        flex-basis: calc(100% / 2 - 20px / 2);
        max-width: calc(100% / 2 - 20px / 2);
        align-items: center;
        background-color: variables.$grey;
        border-radius: 10px;
        padding: 30px 40px;
        gap: 20px;

        @media screen and (max-width: variables.$breakpoint-md-max) {
            flex-direction: column;
        }

        @media screen and (max-width: variables.$breakpoint-sm-max) {
            flex-basis: 100%;
            max-width: 100%;
        }

        i {
            font-size: 48px;
            color: variables.$light-blue;
        }

        p {
            font-family: variables.$font-heading;
            font-style: normal;
            font-weight: 325;
            font-size: 20px;
            line-height: 32px;
            text-align: left;

            @media screen and (max-width: variables.$breakpoint-md-max) {
                text-align: center;
            }
            
            &:last-child {
                margin-bottom: 0;
            }
        }
    }
}

.features__img {
    margin-top: 80px;
    margin-bottom: 50px;
}

.feature__icon {
    flex-shrink: 0;
    width: 80px;
    height: 67px;
    display: flex;
    align-items: center;
    justify-content: center;
}