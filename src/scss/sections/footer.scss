@use "../abstracts/variables" as variables;

// Footer layout styles
.footer {
    background-color: variables.$dark-blue;

    h5 {
        font-family: variables.$font-heading;
        font-size: 22px;
        line-height: 140%;
        color: variables.$light-blue;
        margin-bottom: 20px;
    }

    a {
        text-decoration-thickness: 0%;
        text-decoration-offset: 0%;
    }

    ul {    
        margin-left: 0;
        padding: 0;
        list-style-type: none;
    }

    .row > .col > .row {
        padding: 0;
    }

}

.footer__row {
    padding-top: 60px;
    color: variables.$footer-text-color;
    gap: 80px;
    width: 1100px;
    max-width: 100%;
    margin: 0 auto;

    @media screen and (max-width: variables.$breakpoint-md-max) {
        gap: 40px;
    }

    @media screen and (max-width: variables.$breakpoint-sm-max) {
        gap: 20px;
        flex-wrap: wrap;
        flex-direction: column;
    }

    > .col:nth-child(1) {
        flex-basis: calc(150 / 1040 * 100%);
        max-width: calc(150 / 1040 * 100%);

        @media screen and (max-width: variables.$breakpoint-sm-max) {
            flex-basis: 100%;
            max-width: 100%;
        }
    }

    > .col:nth-child(2) {
        flex-basis: calc(360 / 1040 * 100%);
        max-width: calc(360 / 1040 * 100%);

        @media screen and (max-width: variables.$breakpoint-sm-max) {
            flex-basis: 100%;
            max-width: 100%;

            p {
                width: 360px;
                max-width: 100%;
            }
        }
    }

    > .col:nth-child(3) {
        flex-grow: 1;

        @media screen and (max-width: variables.$breakpoint-sm-max) {
            flex-basis: 100%;
            max-width: 100%;
        }
    }

    a {
        color: variables.$footer-text-color;
        transition: color 0.3s ease-in-out;

        &:hover {
            color: variables.$inverted-text-color;
        }
    }

    li {
        margin-bottom: 6px;
        list-style-type: none;
    }

    p {
        line-height: 120%;
    }

    p, ul {
        margin-bottom: 20px;
    }
}

.footer__contact {
    padding: 0;
    gap: 20px;

    @media screen and (max-width: variables.$breakpoint-sm-max) {
        flex-direction: column;
    }

    img {
        border-radius: 6px;
    }
}

.copyright__row {
    width: 950px;
    max-width: 100%;
    margin: 0 auto;
    padding-top: 55px;
    padding-bottom: 30px;
    color: variables.$inverted-text-color;
    gap: 30px;

    @media screen and (max-width: variables.$breakpoint-sm-max) {
        flex-wrap: wrap;
        flex-direction: column;
        align-items: flex-start;
    }

    p, ul {
        margin-bottom: 0;
    }

    li {
        display: inline-block;
        margin-right: 10px;

        &:last-child {
            margin-right: 0;
        }

        a {
            color: variables.$inverted-text-color;
            transition: color 0.3s ease-in-out;

            &:hover {
                color: variables.$light-blue;
            }
        }
    }

    .col:last-child {
        @media screen and (max-width: variables.$breakpoint-sm-max) {
            order: 1;
        }
    }

    .col:first-child {
        @media screen and (max-width: variables.$breakpoint-sm-max) {
            order: 2;
        }
    }

    .col:nth-child(2) {
        @media screen and (max-width: variables.$breakpoint-sm-max) {
            order: 3;
        }
    }

    .col:nth-child(3) {
        @media screen and (max-width: variables.$breakpoint-sm-max) {
            order: 4;
        }
    }
}

.copyright__links {
    gap: 20px;

    img {
        opacity: 0.6;
        transition: opacity 0.3s ease-in-out;

        &:hover {
            opacity: 1;
        }
    }
}