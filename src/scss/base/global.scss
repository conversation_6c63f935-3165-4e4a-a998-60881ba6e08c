@use '../abstracts/variables' as variables;

// Global styles
html {
    scroll-behavior: smooth;
}

/* Fix header position for all anchor links */
[id] {
    scroll-margin-top: 54px; /* Height of the header */
}

body {
    width: 100%;
    overflow-x: hidden;
    font-family: variables.$font-body;
    font-size: variables.$base-font-size;
    line-height: 1;
    color: variables.$text-color;
}

* {
    box-sizing: border-box;
}

div, p, h1, h2, h3, h4, h5, h6 {
    margin: 0;
    padding: 0;
}

p, h1, h2, h3, h4, h5, h6 {
    margin-bottom: 15px;
}

ul {
    margin-top: 0;
    padding-left: 30px;
}

li {
    list-style-type: disc;
}

section {
    padding: 40px 0;

    h1, h2, h3, h4, h5, h6, p {
        text-align: center;
    }
}

a {
    color: variables.$light-blue;
    text-decoration: underline;
}