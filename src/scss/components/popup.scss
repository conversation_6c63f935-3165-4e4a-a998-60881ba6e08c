@use '../abstracts/variables' as variables;

// Regular popup styles
body.popup-open {
    overflow: hidden;
}

.popup {
    user-select: none;
    opacity: 0;
    visibility: hidden;
    background-color: rgba(0, 0, 0, .7);
    align-items: center;
    display: flex;
    height: 100%;
    justify-content: center;
    left: 0;
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 9999;

    &.show {
        opacity: 1;
        visibility: visible;
    }
}

.admin-bar .popup {
    top: 32px;

    @media screen and (max-width: 782px) {
        top: 46px;
    }
}

.admin-bar.scrolled-under-admin-bar .popup {
    @media screen and (max-width: 600px) {
        top: 0;
    }
}

// Video popup styles (simplified structure)
.popup--video {
    // Video elements are direct children of .popup
    .video-iframe,
    .video-element {
        background-color: #000;
        width: 90vw;
        max-width: 1200px;
        height: auto;
        aspect-ratio: 16/9;
        max-height: 85vh;

        @media screen and (max-width: 1400px) {
            width: 85vw;
        }

        @media screen and (max-width: 1029px) {
            width: 90vw;
            max-height: 70vh;
        }

        @media screen and (max-width: 649px) {
            width: 100%;
            max-height: 60vh;
        }
    }

    .video-element {
        object-fit: contain;
    }
}

.popup__close {
    appearance: none;
    background: none;
    border: none;
    cursor: pointer;
    position: absolute;
    right: 25px;
    top: 25px;
    width: 19px;
    height: 19px;
    display: flex;
    align-items: center;
    justify-content: center;

    @media (max-width: 975px) {
        right: 15px;
        top: 15px;
    }

    &::before {
        content: '\f00d';
        font-family: "Font Awesome 6 Pro";
        -moz-osx-font-smoothing: grayscale;
        -webkit-font-smoothing: antialiased;
        color: variables.$inverted-text-color;
        display: inline-block;
        font-style: normal;
        font-variant: normal;
        line-height: 1;
        text-rendering: auto;
        font-size: 28px;
    }
}

// Calendly popup styles
body .calendly-overlay {
    background-color: rgba(0, 0, 0, .7);
}

body .calendly-overlay .calendly-popup {
    max-height: none!important;
}

.admin-bar .calendly-overlay {
    top: 32px;

    @media screen and (max-width: 782px) {
        top: 46px;
    }
}

.admin-bar.scrolled-under-admin-bar .calendly-overlay {
    @media screen and (max-width: 600px) {
        top: 0;
    }
}

.admin-bar .calendly-overlay .calendly-popup {
    @media (max-width: 975px) {
        top: calc(50px + 32px);
    }

    @media screen and (max-width: 782px) {
        top: calc(50px + 46px);
    }
}

.admin-bar.scrolled-under-admin-bar .calendly-overlay .calendly-popup {
    @media screen and (max-width: 600px) {
        top: 50px;
    }
}

.calendly-spinner {
    display: none!important;
}